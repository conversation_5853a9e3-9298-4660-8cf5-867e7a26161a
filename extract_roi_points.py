#!/usr/bin/env python3
"""
从tm_config.json文件中提取ROIType为1021的GlobalPoints经纬度数据
并按照roi-SQ-056.csv的格式输出
"""

import json
import csv

def extract_roi_points(json_file_path, output_csv_path):
    """
    从JSON文件中提取ROIType为1021的GlobalPoints经纬度数据
    
    Args:
        json_file_path: JSON配置文件路径
        output_csv_path: 输出CSV文件路径
    """
    
    # 读取JSON文件
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 提取ROIType为1021的GlobalPoints
    extracted_points = []
    
    if 'ROIs' in data:
        for roi in data['ROIs']:
            # 检查ROIType是否包含1021
            if 'ROIType' in roi and 1021 in roi['ROIType']:
                if 'GlobalPoints' in roi:
                    for point in roi['GlobalPoints']:
                        if 'lon' in point and 'lat' in point:
                            extracted_points.append({
                                'lon': point['lon'],
                                'lat': point['lat']
                            })
    
    # 写入CSV文件
    with open(output_csv_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        
        # 写入表头
        writer.writerow(['lon', 'lat'])
        
        # 写入数据点
        for point in extracted_points:
            writer.writerow([point['lon'], point['lat']])
    
    print(f"成功提取了 {len(extracted_points)} 个坐标点")
    print(f"数据已保存到: {output_csv_path}")
    
    return extracted_points

if __name__ == "__main__":
    # 设置文件路径
    json_file = "tm_config.json"
    output_file = "extracted_roi_points.csv"
    
    # 执行提取
    points = extract_roi_points(json_file, output_file)
    
    # 显示前几个点作为预览
    print("\n前5个坐标点预览:")
    for i, point in enumerate(points[:5]):
        print(f"{i+1}: lon={point['lon']}, lat={point['lat']}")
