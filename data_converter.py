#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据转换脚本：将 save_1755596388381.txt 格式转换为 det.json 格式
"""

import json
import time
import sys
from typing import Dict, List, Any
import os

def generate_timestamp_sequence(source_timestamp: int) -> Dict[str, int]:
    """
    基于源时间戳生成时间戳序列
    """
    return {
        "timestamp": source_timestamp,
        "timestamp_capture": source_timestamp + 20,
        "timestamp_predicted_out": source_timestamp + 40,
        "timestamp_receive": source_timestamp + 60
    }

def convert_object_to_det_format(obj: Dict[str, Any]) -> Dict[str, Any]:
    """
    将源格式的对象转换为目标格式
    """
    return {
        "id": obj.get("id", ""),
        "uid": str(obj.get("inner_id", "1")),
        "type": obj.get("type", 1),
        "raw_type": "None",
        "center_longitude": obj.get("lon", 0.0),
        "center_latitude": obj.get("lat", 0.0),
        "center_elevation": obj.get("ele", 0.0),
        "center_x": obj.get("lon", 0.0),
        "center_y": obj.get("lat", 0.0),
        "center_z": obj.get("ele", 0.0),
        "length": obj.get("length", 0.0),
        "width": obj.get("width", 0.0),
        "height": obj.get("height", 0.0),
        "speed": obj.get("speed", 0.0),
        "heading": obj.get("heading", 0.0)
    }

def convert_data(input_file: str, output_file: str, max_records: int = None) -> None:
    """
    转换数据文件
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
        max_records: 最大处理记录数（用于测试）
    """
    print(f"开始转换数据...")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"输入文件不存在: {input_file}")
    
    # 获取文件大小用于进度显示
    file_size = os.path.getsize(input_file)
    print(f"文件大小: {file_size / (1024*1024):.2f} MB")
    
    result = []
    processed_count = 0
    error_count = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                # 限制处理记录数（用于测试）
                if max_records and processed_count >= max_records:
                    print(f"达到最大处理记录数限制: {max_records}")
                    break
                
                line = line.strip()
                if not line:
                    continue
                
                try:
                    # 解析JSON行，处理可能的额外字符
                    try:
                        source_data = json.loads(line)
                    except json.JSONDecodeError as e:
                        # 如果有额外数据，截取到有效JSON结束位置
                        if "Extra data" in str(e):
                            truncated_line = line[:e.pos]
                            source_data = json.loads(truncated_line)
                        else:
                            raise

                    # 使用源文件中的时间戳
                    source_timestamp = source_data.get('timestamp', int(time.time() * 1000))
                    timestamps = generate_timestamp_sequence(source_timestamp)

                    # 转换对象数据
                    data_objects = []
                    if 'object_result' in source_data and source_data['object_result']:
                        for obj in source_data['object_result']:
                            converted_obj = convert_object_to_det_format(obj)
                            data_objects.append(converted_obj)

                    # 构建目标格式记录
                    converted_record = {
                        **timestamps,
                        "data_object": data_objects
                    }

                    result.append(converted_record)
                    processed_count += 1
                    
                    # 进度显示
                    if processed_count % 1000 == 0:
                        print(f"已处理 {processed_count} 条记录...")
                    elif processed_count % 100 == 0 and processed_count <= 1000:
                        print(f"已处理 {processed_count} 条记录...")
                
                except json.JSONDecodeError as e:
                    error_count += 1
                    print(f"第 {line_num} 行JSON解析错误: {e}")
                    if error_count > 10:  # 如果错误太多，停止处理
                        print("错误过多，停止处理")
                        break
                except Exception as e:
                    error_count += 1
                    print(f"第 {line_num} 行处理错误: {e}")
    
    except Exception as e:
        print(f"文件读取错误: {e}")
        return
    
    print(f"数据转换完成!")
    print(f"成功处理: {processed_count} 条记录")
    print(f"错误记录: {error_count} 条")
    
    # 写入输出文件
    print(f"正在写入输出文件...")
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=4, ensure_ascii=False)
        print(f"输出文件写入成功: {output_file}")
        
        # 显示输出文件信息
        output_size = os.path.getsize(output_file)
        print(f"输出文件大小: {output_size / (1024*1024):.2f} MB")
        
    except Exception as e:
        print(f"输出文件写入错误: {e}")

def test_conversion(input_file: str, test_records: int = 5) -> None:
    """
    测试转换功能，只处理前几条记录
    """
    print("=" * 50)
    print("开始测试转换...")
    print("=" * 50)
    
    test_output = "test_det.json"
    convert_data(input_file, test_output, max_records=test_records)
    
    # 验证输出文件
    if os.path.exists(test_output):
        print("\n测试结果验证:")
        with open(test_output, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        
        print(f"转换后记录数: {len(test_data)}")
        if test_data:
            first_record = test_data[0]
            print(f"第一条记录的时间戳: {first_record.get('timestamp')}")
            print(f"第一条记录的对象数: {len(first_record.get('data_object', []))}")
            
            if first_record.get('data_object'):
                first_obj = first_record['data_object'][0]
                print(f"第一个对象的ID: {first_obj.get('id')}")
                print(f"第一个对象的坐标: ({first_obj.get('center_longitude')}, {first_obj.get('center_latitude')})")

if __name__ == "__main__":
    input_file = "save_1755596388381.txt"
    output_file = "converted_det.json"
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            # 测试模式
            test_conversion(input_file)
        elif sys.argv[1] == "full":
            # 完整转换
            convert_data(input_file, output_file)
        else:
            print("用法: python data_converter.py [test|full]")
    else:
        # 默认测试模式
        test_conversion(input_file)
