#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON数据提取脚本
从JSON文件中提取dev_no和timestamp_readable字段到CSV
"""

import json
import csv
import sys
from typing import List, Dict, Optional


class JSONExtractor:
    """JSON数据提取器"""
    
    def __init__(self):
        pass
    
    def extract_fields(self, json_line: str) -> Optional[Dict[str, str]]:
        """
        从JSON行中提取指定字段
        
        Args:
            json_line: JSON字符串
            
        Returns:
            包含提取字段的字典，如果解析失败返回None
        """
        try:
            # 清理行数据，移除首尾空白字符和空字符
            clean_line = json_line.strip().replace('\x00', '')
            if not clean_line:
                return None
                
            data = json.loads(clean_line)
            
            # 提取dev_no和timestamp_readable字段
            dev_no = data.get('dev_no', '')
            timestamp_readable = data.get('timestamp_readable', '')
            
            return {
                'dev_no': dev_no,
                'timestamp_readable': timestamp_readable
            }
            
        except (json.JSONDecodeError, KeyError) as e:
            print(f"JSON解析错误: {e}")
            return None
    
    def process_file(self, input_file: str) -> List[Dict[str, str]]:
        """
        处理JSON文件
        
        Args:
            input_file: 输入文件路径
            
        Returns:
            提取结果列表
        """
        results = []
        
        try:
            with open(input_file, 'r', encoding='utf-8') as file:
                for line_num, line in enumerate(file, 1):
                    if line.strip():  # 跳过空行
                        extracted = self.extract_fields(line)
                        if extracted:
                            extracted['line_number'] = line_num
                            results.append(extracted)
                        else:
                            print(f"警告: 第{line_num}行JSON解析失败")
                            
        except FileNotFoundError:
            print(f"错误: 文件 '{input_file}' 不存在")
        except Exception as e:
            print(f"错误: 读取文件时发生异常: {e}")
            
        return results
    
    def save_to_csv(self, results: List[Dict[str, str]], output_file: str):
        """
        将结果保存为CSV文件
        
        Args:
            results: 提取结果列表
            output_file: 输出文件路径
        """
        if not results:
            print("没有数据可保存")
            return
            
        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['line_number', 'dev_no', 'timestamp_readable']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for result in results:
                    writer.writerow(result)
                    
            print(f"结果已保存到: {output_file}")
            
        except Exception as e:
            print(f"错误: 保存CSV文件时发生异常: {e}")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python json_extractor.py <JSON文件路径> [输出CSV文件路径]")
        print("示例: python json_extractor.py data.txt output.csv")
        return
        
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 创建提取器
    extractor = JSONExtractor()
    
    # 处理文件
    print(f"正在处理文件: {input_file}")
    results = extractor.process_file(input_file)
    
    if not results:
        print("未找到有效的JSON数据")
        return
        
    print(f"成功提取 {len(results)} 条记录")
    
    # 显示前几条记录
    print("\n前5条记录:")
    print("-" * 80)
    for i, result in enumerate(results[:5]):
        print(f"记录 {i+1}:")
        print(f"  行号: {result['line_number']}")
        print(f"  设备号: {result['dev_no']}")
        print(f"  时间戳: {result['timestamp_readable']}")
        print()
    
    # 保存到CSV文件
    if output_file:
        extractor.save_to_csv(results, output_file)
    else:
        # 自动生成输出文件名
        import os
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_extracted.csv"
        extractor.save_to_csv(results, output_file)


if __name__ == "__main__":
    main()
