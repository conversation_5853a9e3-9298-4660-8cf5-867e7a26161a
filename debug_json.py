#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试JSON解析问题
"""

import json

def debug_json_parsing(filename, line_num=1):
    """调试JSON解析"""
    with open(filename, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f, 1):
            if i != line_num:
                continue
                
            line = line.strip()
            print(f"第 {i} 行长度: {len(line)}")
            
            # 尝试直接解析
            try:
                data = json.loads(line)
                print("JSON解析成功!")
                print(f"键: {list(data.keys())}")
                return data
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                print(f"错误位置: {e.pos}")
                
                # 检查错误位置附近的内容
                start = max(0, e.pos - 50)
                end = min(len(line), e.pos + 50)
                print(f"错误位置附近内容: {line[start:end]}")
                
                # 尝试截取到错误位置
                truncated = line[:e.pos]
                print(f"截取到错误位置的长度: {len(truncated)}")
                
                try:
                    partial_data = json.loads(truncated)
                    print("截取部分可以解析!")
                    return partial_data
                except:
                    print("截取部分也无法解析")
                
                # 检查是否有多余的字符
                if e.pos < len(line):
                    extra_data = line[e.pos:]
                    print(f"多余数据长度: {len(extra_data)}")
                    print(f"多余数据前100字符: {extra_data[:100]}")
                
                return None

if __name__ == "__main__":
    result = debug_json_parsing("save_1755596388381.txt", 1)
    if result:
        print(f"\n成功解析的数据示例:")
        print(f"timestamp: {result.get('timestamp')}")
        print(f"object_count: {result.get('object_count')}")
        if 'object_result' in result and result['object_result']:
            first_obj = result['object_result'][0]
            print(f"第一个对象ID: {first_obj.get('id')}")
            print(f"第一个对象坐标: ({first_obj.get('lon')}, {first_obj.get('lat')})")
