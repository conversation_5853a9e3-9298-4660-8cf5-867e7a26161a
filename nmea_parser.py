#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NMEA数据解析脚本
提取GNGGA或GPGGA语句中的UTC时间和经纬度信息
"""

import re
import sys
from datetime import datetime, timezone, timedelta
from typing import List, Tuple, Optional


class NMEAParser:
    """NMEA数据解析器"""
    
    def __init__(self):
        # GNGGA和GPGGA语句的正则表达式 - 更灵活的匹配模式
        # 匹配所有逗号分隔的字段，最后是*校验和
        self.gga_pattern = re.compile(r'^(\$G[PN]GGA),(.+)\*([0-9A-F]{2})$')
    
    def parse_coordinate(self, coord_str: str, direction: str) -> Optional[float]:
        """
        解析NMEA坐标格式为十进制度数
        
        Args:
            coord_str: 坐标字符串 (如: 3958.123456)
            direction: 方向 (N/S/E/W)
            
        Returns:
            十进制度数，如果解析失败返回None
        """
        if not coord_str or not direction:
            return None
            
        try:
            # NMEA格式: DDMM.MMMMMM (度分格式)
            if len(coord_str) < 4:
                return None
                
            # 分离度和分
            if '.' in coord_str:
                dot_pos = coord_str.index('.')
                if dot_pos < 2:
                    return None
                degrees = int(coord_str[:dot_pos-2])
                minutes = float(coord_str[dot_pos-2:])
            else:
                degrees = int(coord_str[:-2])
                minutes = float(coord_str[-2:])
            
            # 转换为十进制度数
            decimal_degrees = degrees + minutes / 60.0
            
            # 根据方向调整符号
            if direction in ['S', 'W']:
                decimal_degrees = -decimal_degrees
                
            return decimal_degrees
            
        except (ValueError, IndexError):
            return None
    
    def parse_time(self, time_str: str, date_str: str = None) -> Optional[dict]:
        """
        解析NMEA时间格式并转换为UTC+8时区

        Args:
            time_str: 时间字符串 (HHMMSS.SSS)
            date_str: 日期字符串 (可选，如果没有提供则使用当前日期)

        Returns:
            包含原始UTC时间和UTC+8时间的字典，如果解析失败返回None
        """
        if not time_str or len(time_str) < 6:
            return None

        try:
            # 提取时分秒
            hours = int(time_str[:2])
            minutes = int(time_str[2:4])
            seconds = float(time_str[4:])

            # 验证时间有效性
            if hours > 23 or minutes > 59 or seconds >= 60:
                return None

            # 如果没有提供日期，使用当前日期
            if date_str is None:
                current_date = datetime.now().date()
            else:
                # 这里可以扩展以支持日期解析，目前使用当前日期
                current_date = datetime.now().date()

            # 创建UTC时间
            utc_time = datetime.combine(current_date, datetime.min.time().replace(
                hour=hours, minute=minutes, second=int(seconds),
                microsecond=int((seconds % 1) * 1000000)
            )).replace(tzinfo=timezone.utc)

            # 转换为UTC+8时区
            utc_plus_8 = timezone(timedelta(hours=8))
            local_time = utc_time.astimezone(utc_plus_8)

            # 格式化时间字符串
            utc_formatted = f"{hours:02d}:{minutes:02d}:{seconds:06.3f}"
            local_formatted = local_time.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3]  # 保留3位毫秒

            return {
                'utc_time': utc_formatted,
                'utc_plus_8': local_formatted,
                'datetime_utc': utc_time,
                'datetime_local': local_time
            }

        except (ValueError, IndexError):
            return None
    
    def parse_gga_line(self, line: str) -> Optional[dict]:
        """
        解析单行GGA数据

        Args:
            line: NMEA语句行

        Returns:
            解析结果字典，包含时间、经纬度等信息
        """
        line = line.strip()
        match = self.gga_pattern.match(line)

        if not match:
            return None

        sentence_type = match.group(1)  # $GNGGA 或 $GPGGA
        data_part = match.group(2)      # 数据部分
        checksum = match.group(3)       # 校验和

        # 分割数据字段
        fields = data_part.split(',')

        # 确保至少有基本的字段数量
        if len(fields) < 10:
            return None

        try:
            utc_time = fields[0]      # UTC时间
            latitude = fields[1]      # 纬度
            lat_dir = fields[2]       # 纬度方向 (N/S)
            longitude = fields[3]     # 经度
            lon_dir = fields[4]       # 经度方向 (E/W)
            quality = fields[5]       # 定位质量
            num_sats = fields[6]      # 卫星数量
            hdop = fields[7]          # 水平精度因子
            altitude = fields[8]      # 海拔高度
            alt_unit = fields[9] if len(fields) > 9 else ''      # 海拔单位

            # 解析时间
            parsed_time = self.parse_time(utc_time)
            if not parsed_time:
                return None

            # 解析坐标
            lat_decimal = self.parse_coordinate(latitude, lat_dir)
            lon_decimal = self.parse_coordinate(longitude, lon_dir)

            if lat_decimal is None or lon_decimal is None:
                return None

            return {
                'sentence_type': sentence_type,
                'utc_time': parsed_time['utc_time'],
                'utc_plus_8_time': parsed_time['utc_plus_8'],
                'latitude': lat_decimal,
                'longitude': lon_decimal,
                'quality': quality,
                'num_satellites': num_sats,
                'hdop': hdop,
                'altitude': altitude,
                'raw_line': line
            }

        except (IndexError, ValueError) as e:
            return None
    
    def parse_file(self, filename: str) -> List[dict]:
        """
        解析NMEA文件
        
        Args:
            filename: 文件路径
            
        Returns:
            解析结果列表
        """
        results = []
        
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                for line_num, line in enumerate(file, 1):
                    if 'GGA' in line and ('$GNGGA' in line or '$GPGGA' in line):
                        parsed = self.parse_gga_line(line)
                        if parsed:
                            parsed['line_number'] = line_num
                            results.append(parsed)
                        else:
                            print(f"警告: 第{line_num}行解析失败: {line.strip()}")
                            
        except FileNotFoundError:
            print(f"错误: 文件 '{filename}' 不存在")
        except Exception as e:
            print(f"错误: 读取文件时发生异常: {e}")
            
        return results
    
    def save_to_csv(self, results: List[dict], output_file: str):
        """
        将结果保存为CSV文件
        
        Args:
            results: 解析结果列表
            output_file: 输出文件路径
        """
        import csv
        
        if not results:
            print("没有数据可保存")
            return
            
        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['line_number', 'sentence_type', 'utc_time', 'utc_plus_8_time', 'latitude', 'longitude',
                             'quality', 'num_satellites', 'hdop', 'altitude']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for result in results:
                    # 只写入需要的字段
                    row = {k: result.get(k, '') for k in fieldnames}
                    writer.writerow(row)
                    
            print(f"结果已保存到: {output_file}")
            
        except Exception as e:
            print(f"错误: 保存CSV文件时发生异常: {e}")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python nmea_parser.py <NMEA文件路径> [输出CSV文件路径]")
        print("示例: python nmea_parser.py data.nmea output.csv")
        return
        
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 创建解析器
    parser = NMEAParser()
    
    # 解析文件
    print(f"正在解析文件: {input_file}")
    results = parser.parse_file(input_file)
    
    if not results:
        print("未找到有效的GNGGA或GPGGA数据")
        return
        
    print(f"成功解析 {len(results)} 条记录")
    
    # 显示前几条记录
    print("\n前5条记录:")
    print("-" * 100)
    for i, result in enumerate(results[:5]):
        print(f"记录 {i+1}:")
        print(f"  行号: {result['line_number']}")
        print(f"  类型: {result['sentence_type']}")
        print(f"  UTC时间: {result['utc_time']}")
        print(f"  UTC+8时间: {result['utc_plus_8_time']}")
        print(f"  纬度: {result['latitude']:.6f}°")
        print(f"  经度: {result['longitude']:.6f}°")
        print(f"  定位质量: {result['quality']}")
        print(f"  卫星数: {result['num_satellites']}")
        print()
    
    # 保存到CSV文件
    if output_file:
        parser.save_to_csv(results, output_file)
    else:
        # 自动生成输出文件名
        import os
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_parsed.csv"
        parser.save_to_csv(results, output_file)


if __name__ == "__main__":
    main()



