#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NMEA数据解析脚本
提取GNGGA或GPGGA语句中的UTC时间和经纬度信息
"""

import re
import sys
from datetime import datetime, timezone, timedelta
from typing import List, Tuple, Optional


class NMEAParser:
    """NMEA数据解析器"""
    
    def __init__(self):
        # GNGGA和GPGGA语句的正则表达式 - 更灵活的匹配模式
        # 匹配所有逗号分隔的字段，最后是*校验和
        self.gga_pattern = re.compile(r'^(\$G[PN]GGA),(.+)\*([0-9A-F]{2})$')
        # GPRMC语句的正则表达式
        self.rmc_pattern = re.compile(r'^(\$G[PN]RMC),(.+)\*([0-9A-F]{2})$')
    
    def parse_coordinate(self, coord_str: str, direction: str) -> Optional[float]:
        """
        解析NMEA坐标格式为十进制度数
        
        Args:
            coord_str: 坐标字符串 (如: 3958.123456)
            direction: 方向 (N/S/E/W)
            
        Returns:
            十进制度数，如果解析失败返回None
        """
        if not coord_str or not direction:
            return None
            
        try:
            # NMEA格式: DDMM.MMMMMM (度分格式)
            if len(coord_str) < 4:
                return None
                
            # 分离度和分
            if '.' in coord_str:
                dot_pos = coord_str.index('.')
                if dot_pos < 2:
                    return None
                degrees = int(coord_str[:dot_pos-2])
                minutes = float(coord_str[dot_pos-2:])
            else:
                degrees = int(coord_str[:-2])
                minutes = float(coord_str[-2:])
            
            # 转换为十进制度数
            decimal_degrees = degrees + minutes / 60.0
            
            # 根据方向调整符号
            if direction in ['S', 'W']:
                decimal_degrees = -decimal_degrees
                
            return decimal_degrees
            
        except (ValueError, IndexError):
            return None
    
    def parse_time(self, time_str: str, date_obj: datetime = None) -> Optional[dict]:
        """
        解析NMEA时间格式并转换为UTC+8时区

        Args:
            time_str: 时间字符串 (HHMMSS.SSS)
            date_obj: 日期对象 (可选，如果没有提供则使用当前日期)

        Returns:
            包含原始UTC时间和UTC+8时间的字典，如果解析失败返回None
        """
        if not time_str or len(time_str) < 6:
            return None

        try:
            # 提取时分秒
            hours = int(time_str[:2])
            minutes = int(time_str[2:4])
            seconds = float(time_str[4:])

            # 验证时间有效性
            if hours > 23 or minutes > 59 or seconds >= 60:
                return None

            # 如果没有提供日期，使用当前日期
            if date_obj is None:
                current_date = datetime.now().date()
            else:
                current_date = date_obj.date()

            # 创建UTC时间
            utc_time = datetime.combine(current_date, datetime.min.time().replace(
                hour=hours, minute=minutes, second=int(seconds),
                microsecond=int((seconds % 1) * 1000000)
            )).replace(tzinfo=timezone.utc)

            # 转换为UTC+8时区
            utc_plus_8 = timezone(timedelta(hours=8))
            local_time = utc_time.astimezone(utc_plus_8)

            # 格式化时间字符串
            utc_formatted = f"{hours:02d}:{minutes:02d}:{seconds:06.3f}"
            local_formatted = local_time.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3]  # 保留3位毫秒

            return {
                'utc_time': utc_formatted,
                'utc_plus_8': local_formatted,
                'datetime_utc': utc_time,
                'datetime_local': local_time
            }

        except (ValueError, IndexError):
            return None

    def parse_date(self, date_str: str) -> Optional[datetime]:
        """
        解析NMEA日期格式 (DDMMYY)

        Args:
            date_str: 日期字符串 (DDMMYY)

        Returns:
            datetime对象，如果解析失败返回None
        """
        if not date_str or len(date_str) != 6:
            return None

        try:
            day = int(date_str[:2])
            month = int(date_str[2:4])
            year = int(date_str[4:6])

            # 处理年份 - 假设00-99对应2000-2099
            if year < 50:
                year += 2000
            else:
                year += 1900

            return datetime(year, month, day)

        except (ValueError, IndexError):
            return None
    
    def parse_gga_line(self, line: str) -> Optional[dict]:
        """
        解析单行GGA数据

        Args:
            line: NMEA语句行

        Returns:
            解析结果字典，包含时间、经纬度等信息
        """
        line = line.strip()
        match = self.gga_pattern.match(line)

        if not match:
            return None

        sentence_type = match.group(1)  # $GNGGA 或 $GPGGA
        data_part = match.group(2)      # 数据部分
        checksum = match.group(3)       # 校验和

        # 分割数据字段
        fields = data_part.split(',')

        # 确保至少有基本的字段数量
        if len(fields) < 10:
            return None

        try:
            utc_time = fields[0]      # UTC时间
            latitude = fields[1]      # 纬度
            lat_dir = fields[2]       # 纬度方向 (N/S)
            longitude = fields[3]     # 经度
            lon_dir = fields[4]       # 经度方向 (E/W)
            quality = fields[5]       # 定位质量
            num_sats = fields[6]      # 卫星数量
            hdop = fields[7]          # 水平精度因子
            altitude = fields[8]      # 海拔高度
            alt_unit = fields[9] if len(fields) > 9 else ''      # 海拔单位

            # 解析时间
            parsed_time = self.parse_time(utc_time)
            if not parsed_time:
                return None

            # 解析坐标
            lat_decimal = self.parse_coordinate(latitude, lat_dir)
            lon_decimal = self.parse_coordinate(longitude, lon_dir)

            if lat_decimal is None or lon_decimal is None:
                return None

            return {
                'sentence_type': sentence_type,
                'utc_time': parsed_time['utc_time'],
                'utc_plus_8_time': parsed_time['utc_plus_8'],
                'latitude': lat_decimal,
                'longitude': lon_decimal,
                'quality': quality,
                'num_satellites': num_sats,
                'hdop': hdop,
                'altitude': altitude,
                'raw_line': line
            }

        except (IndexError, ValueError) as e:
            return None

    def parse_rmc_line(self, line: str) -> Optional[dict]:
        """
        解析单行RMC数据

        Args:
            line: NMEA语句行

        Returns:
            解析结果字典，包含时间、日期、经纬度等信息
        """
        line = line.strip()
        match = self.rmc_pattern.match(line)

        if not match:
            return None

        sentence_type = match.group(1)  # $GPRMC 或 $GNRMC
        data_part = match.group(2)      # 数据部分
        checksum = match.group(3)       # 校验和

        # 分割数据字段
        fields = data_part.split(',')

        # 确保至少有基本的字段数量
        if len(fields) < 9:
            return None

        try:
            utc_time = fields[0]      # UTC时间
            status = fields[1]        # 状态 (A=有效, V=无效)
            latitude = fields[2]      # 纬度
            lat_dir = fields[3]       # 纬度方向 (N/S)
            longitude = fields[4]     # 经度
            lon_dir = fields[5]       # 经度方向 (E/W)
            speed = fields[6]         # 速度 (节)
            course = fields[7]        # 航向
            date = fields[8]          # 日期 (DDMMYY)

            # 检查状态是否有效
            if status != 'A':
                return None

            # 解析日期
            parsed_date = self.parse_date(date)
            if not parsed_date:
                return None

            # 解析时间（使用实际日期）
            parsed_time = self.parse_time(utc_time, parsed_date)
            if not parsed_time:
                return None

            # 解析坐标
            lat_decimal = self.parse_coordinate(latitude, lat_dir)
            lon_decimal = self.parse_coordinate(longitude, lon_dir)

            if lat_decimal is None or lon_decimal is None:
                return None

            return {
                'sentence_type': sentence_type,
                'utc_time': parsed_time['utc_time'],
                'utc_plus_8_time': parsed_time['utc_plus_8'],
                'date': date,
                'latitude': lat_decimal,
                'longitude': lon_decimal,
                'status': status,
                'speed_knots': speed,
                'course': course,
                'raw_line': line
            }

        except (IndexError, ValueError) as e:
            return None
    
    def parse_file(self, filename: str) -> List[dict]:
        """
        解析NMEA文件，合并同一时间点的GGA和RMC数据

        Args:
            filename: 文件路径

        Returns:
            解析结果列表
        """
        # 使用字典来存储按时间分组的数据
        time_groups = {}
        line_numbers = {}

        try:
            with open(filename, 'r', encoding='utf-8') as file:
                for line_num, line in enumerate(file, 1):
                    parsed = None
                    if 'GGA' in line and ('$GNGGA' in line or '$GPGGA' in line):
                        parsed = self.parse_gga_line(line)
                        if parsed:
                            time_key = parsed['utc_time']
                            if time_key not in time_groups:
                                time_groups[time_key] = {}
                                line_numbers[time_key] = line_num
                            time_groups[time_key]['gga'] = parsed

                    elif 'RMC' in line and ('$GPRMC' in line or '$GNRMC' in line):
                        parsed = self.parse_rmc_line(line)
                        if parsed:
                            time_key = parsed['utc_time']
                            if time_key not in time_groups:
                                time_groups[time_key] = {}
                                line_numbers[time_key] = line_num
                            time_groups[time_key]['rmc'] = parsed

                    if not parsed and ('GGA' in line or 'RMC' in line):
                        print(f"警告: 第{line_num}行解析失败: {line.strip()}")

        except FileNotFoundError:
            print(f"错误: 文件 '{filename}' 不存在")
            return []
        except Exception as e:
            print(f"错误: 读取文件时发生异常: {e}")
            return []

        # 合并数据并生成结果
        results = []
        for time_key in sorted(time_groups.keys()):
            group = time_groups[time_key]
            merged_record = self.merge_gga_rmc_data(group, line_numbers[time_key])
            if merged_record:
                results.append(merged_record)

        return results

    def merge_gga_rmc_data(self, group: dict, line_number: int) -> Optional[dict]:
        """
        合并同一时间点的GGA和RMC数据

        Args:
            group: 包含gga和/或rmc数据的字典
            line_number: 行号

        Returns:
            合并后的数据记录
        """
        gga_data = group.get('gga')
        rmc_data = group.get('rmc')

        # 如果两种数据都没有，返回None
        if not gga_data and not rmc_data:
            return None

        # 创建合并记录
        merged = {
            'line_number': line_number,
            'sentence_type': '',
            'utc_time': '',
            'utc_plus_8_time': '',
            'date': '',
            'latitude': None,
            'longitude': None,
            'quality': '',
            'num_satellites': '',
            'hdop': '',
            'altitude': '',
            'status': '',
            'speed_knots': '',
            'course': ''
        }

        # 优先使用RMC的时间信息（包含实际日期）
        if rmc_data:
            merged['utc_time'] = rmc_data['utc_time']
            merged['utc_plus_8_time'] = rmc_data['utc_plus_8_time']
            merged['date'] = rmc_data['date']
            merged['latitude'] = rmc_data['latitude']
            merged['longitude'] = rmc_data['longitude']
            merged['status'] = rmc_data['status']
            merged['speed_knots'] = rmc_data['speed_knots']
            merged['course'] = rmc_data['course']
            merged['sentence_type'] = rmc_data['sentence_type']

        # 使用GGA的定位质量信息
        if gga_data:
            # 如果没有RMC数据，使用GGA的基本信息
            if not rmc_data:
                merged['utc_time'] = gga_data['utc_time']
                merged['utc_plus_8_time'] = gga_data['utc_plus_8_time']
                merged['latitude'] = gga_data['latitude']
                merged['longitude'] = gga_data['longitude']
                merged['sentence_type'] = gga_data['sentence_type']

            # 总是使用GGA的质量信息
            merged['quality'] = gga_data['quality']
            merged['num_satellites'] = gga_data['num_satellites']
            merged['hdop'] = gga_data['hdop']
            merged['altitude'] = gga_data['altitude']

        # 设置组合的语句类型
        if gga_data and rmc_data:
            merged['sentence_type'] = f"{gga_data['sentence_type']}+{rmc_data['sentence_type']}"

        return merged
    
    def save_to_csv(self, results: List[dict], output_file: str):
        """
        将结果保存为CSV文件
        
        Args:
            results: 解析结果列表
            output_file: 输出文件路径
        """
        import csv
        
        if not results:
            print("没有数据可保存")
            return
            
        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['line_number', 'sentence_type', 'utc_time', 'utc_plus_8_time', 'date', 'latitude', 'longitude',
                             'quality', 'num_satellites', 'hdop', 'altitude', 'status', 'speed_knots', 'course']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for result in results:
                    # 只写入需要的字段
                    row = {k: result.get(k, '') for k in fieldnames}
                    writer.writerow(row)
                    
            print(f"结果已保存到: {output_file}")
            
        except Exception as e:
            print(f"错误: 保存CSV文件时发生异常: {e}")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python nmea_parser.py <NMEA文件路径> [输出CSV文件路径]")
        print("示例: python nmea_parser.py data.nmea output.csv")
        return
        
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 创建解析器
    parser = NMEAParser()
    
    # 解析文件
    print(f"正在解析文件: {input_file}")
    results = parser.parse_file(input_file)
    
    if not results:
        print("未找到有效的GNGGA、GPGGA或GPRMC数据")
        return

    print(f"成功解析并合并 {len(results)} 条记录")

    # 显示前几条记录
    print("\n前5条记录:")
    print("-" * 120)
    for i, result in enumerate(results[:5]):
        print(f"记录 {i+1}:")
        print(f"  行号: {result['line_number']}")
        print(f"  类型: {result['sentence_type']}")
        print(f"  UTC时间: {result['utc_time']}")
        print(f"  UTC+8时间: {result['utc_plus_8_time']}")
        if result['date']:
            print(f"  日期: {result['date']}")
        if result['latitude'] is not None:
            print(f"  纬度: {result['latitude']:.6f}°")
            print(f"  经度: {result['longitude']:.6f}°")
        if result['quality']:
            print(f"  定位质量: {result['quality']}")
        if result['num_satellites']:
            print(f"  卫星数: {result['num_satellites']}")
        if result['hdop']:
            print(f"  水平精度: {result['hdop']}")
        if result['altitude']:
            print(f"  海拔: {result['altitude']} M")
        if result['speed_knots']:
            print(f"  速度: {result['speed_knots']} 节")
        if result['course']:
            print(f"  航向: {result['course']}°")
        print()
    
    # 保存到CSV文件
    if output_file:
        parser.save_to_csv(results, output_file)
    else:
        # 自动生成输出文件名
        import os
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_parsed.csv"
        parser.save_to_csv(results, output_file)


if __name__ == "__main__":
    main()



