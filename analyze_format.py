#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析源文件格式
"""

import json
import re

def analyze_file_format(filename, max_lines=5):
    """分析文件格式"""
    print(f"分析文件: {filename}")
    
    with open(filename, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if i >= max_lines:
                break
                
            line = line.strip()
            print(f"\n第 {i+1} 行:")
            print(f"长度: {len(line)}")
            print(f"前100字符: {line[:100]}")
            print(f"后100字符: {line[-100:]}")
            
            # 检查是否包含多个JSON对象
            brace_count = line.count('{')
            print(f"包含 '{{' 的数量: {brace_count}")
            
            # 尝试找到JSON对象的分界点
            json_objects = []
            current_obj = ""
            brace_level = 0
            
            for char in line:
                current_obj += char
                if char == '{':
                    brace_level += 1
                elif char == '}':
                    brace_level -= 1
                    if brace_level == 0:
                        json_objects.append(current_obj)
                        current_obj = ""
            
            print(f"检测到的JSON对象数量: {len(json_objects)}")
            
            # 尝试解析第一个JSON对象
            if json_objects:
                try:
                    first_obj = json.loads(json_objects[0])
                    print(f"第一个JSON对象的键: {list(first_obj.keys())}")
                    if 'object_result' in first_obj:
                        print(f"object_result数量: {len(first_obj['object_result'])}")
                except Exception as e:
                    print(f"JSON解析错误: {e}")

if __name__ == "__main__":
    analyze_file_format("save_1755596388381.txt")
