#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证转换结果
"""

import json

def validate_conversion_result(filename):
    """验证转换结果"""
    print(f"验证文件: {filename}")
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✓ JSON格式正确")
        print(f"✓ 记录数: {len(data)}")
        
        if data:
            first_record = data[0]
            last_record = data[-1]
            
            # 检查必需字段
            required_fields = ['timestamp', 'timestamp_capture', 'timestamp_predicted_out', 'timestamp_receive', 'data_object']
            for field in required_fields:
                if field in first_record:
                    print(f"✓ 包含字段: {field}")
                else:
                    print(f"✗ 缺少字段: {field}")
            
            print(f"✓ 第一条记录对象数: {len(first_record.get('data_object', []))}")
            print(f"✓ 最后一条记录对象数: {len(last_record.get('data_object', []))}")
            print(f"✓ 第一条记录时间戳: {first_record.get('timestamp')}")
            print(f"✓ 最后一条记录时间戳: {last_record.get('timestamp')}")
            
            # 检查对象格式
            if first_record.get('data_object'):
                first_obj = first_record['data_object'][0]
                obj_fields = ['id', 'uid', 'type', 'raw_type', 'center_longitude', 'center_latitude', 
                             'center_elevation', 'center_x', 'center_y', 'center_z', 
                             'length', 'width', 'height', 'speed', 'heading']
                
                print("\n对象字段检查:")
                for field in obj_fields:
                    if field in first_obj:
                        print(f"✓ {field}: {first_obj[field]}")
                    else:
                        print(f"✗ 缺少字段: {field}")
        
        print(f"\n验证完成！")
        
    except Exception as e:
        print(f"✗ 验证失败: {e}")

if __name__ == "__main__":
    import sys
    filename = sys.argv[1] if len(sys.argv) > 1 else "medium_test_det.json"
    validate_conversion_result(filename)
